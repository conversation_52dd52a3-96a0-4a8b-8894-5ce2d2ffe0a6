.copy {
    position: relative;
}

.cut {
    position: relative;
}

.copy::after,
.cut::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    border-radius: 10%;
}

.copy::after {
    background-color: rgba(142, 124, 108, 0.7);
}

.cut::after {
    background-color: rgba(144, 76, 115, 0.7);
}

.nav-file-title.is-active.copy,
.nav-file-title.is-active.cut {
    background-color: transparent !important;
}

/* @keyframes flash {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
} */

/* .nav-file-title.is-active.reveal {
    background-color: rgb(101, 98, 75);
    animation: flash 0.5s ease-in-out;
} */