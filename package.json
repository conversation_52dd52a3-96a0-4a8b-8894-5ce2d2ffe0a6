{"name": "explorer-shortcuts", "version": "1.4.1", "description": "Open successive files navigating with arrows ↑↓ over the explorer and use shortcuts for current actions on files/folders (cut, paste, copy, rename, delete, etc).", "main": "main.js", "scripts": {"start": "yarn install && yarn dev", "dev": "tsx scripts/esbuild.config.ts", "build": "tsc -noEmit -skipLibCheck && tsx scripts/esbuild.config.ts production", "real": "tsx scripts/esbuild.config.ts production real", "acp": "tsx scripts/acp.ts", "bacp": "tsx scripts/acp.ts -b", "version": "tsx scripts/update-version.mts", "release": "tsx scripts/release.ts", "update-version": "tsx scripts/update-version.ts", "v": "tsx scripts/update-version.ts", "r": "tsx scripts/release.ts", "help": "tsx scripts/help.ts", "h": "tsx scripts/help.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "keywords": [], "author": "3C0D", "license": "MIT", "devDependencies": {"@types/eslint": "latest", "@types/fs-extra": "^11.0.4", "@types/node": "^22.15.26", "@types/semver": "^7.7.0", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "builtin-modules": "3.3.0", "cross-env": "^7.0.3", "dedent": "^1.6.0", "dotenv": "^16.4.5", "esbuild": "latest", "eslint": "latest", "eslint-import-resolver-typescript": "latest", "fs-extra": "^11.2.0", "jiti": "latest", "json-stringify-pretty-compact": "^4.0.0", "obsidian": "*", "obsidian-typings": "^3.9.5", "semver": "^7.7.2", "tslib": "2.4.0", "tsx": "^4.19.4", "typescript": "^5.8.2"}, "engines": {"npm": "please-use-yarn", "yarn": ">=1.22.0"}, "type": "module"}