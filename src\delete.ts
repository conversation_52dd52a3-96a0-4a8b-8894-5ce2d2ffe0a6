import { Notice, TFile, <PERSON>F<PERSON>er, <PERSON><PERSON>, Setting } from "obsidian";
import type ExplorerShortcuts from "./main.ts";
import { confirmation } from "./modal.ts";
import { getExplorerView, getHoveredElement, getElPath } from "./utils.ts";

interface DeleteAction {
    action: 'delete' | 'skip' | 'cancel';
    applyToAll?: boolean;
}

export async function deleteItem(plugin: ExplorerShortcuts, e: KeyboardEvent): Promise<void> {
    const view = getExplorerView(plugin);
    const tree = view?.tree;
    if (!tree) return;

    // Get all selected items (using similar logic as cut-copy)
    const selectedItems = getSelectedItemsForDelete(plugin);

    console.log(`🗑️ DEBUG: Found ${selectedItems.length} selected items for deletion`);
    selectedItems.forEach((item, index) => {
        console.log(`🗑️ Item ${index + 1}: ${item.file?.name} (${item.file instanceof TFile ? 'FILE' : 'FOLDER'})`);
    });

    if (selectedItems.length === 0) {
        // Fallback to single hovered item
        const hovered = getHoveredElement(plugin);
        const path = getElPath(hovered) || '/';
        const hoveredItem = view.fileItems[path];
        if (hoveredItem) {
            selectedItems.push(hoveredItem);
            console.log(`🗑️ Fallback to hovered: ${hoveredItem.file?.name}`);
        }
    }

    if (selectedItems.length === 0) return;

    // Check if any item has focus (to allow default obsidian delete)
    // Only skip if we have a single focused item (renaming scenario)
    const hasFocusedItem = selectedItems.length === 1 &&
        selectedItems[0].file instanceof TFile &&
        selectedItems[0].el.children[0].classList.contains("has-focus");

    if (hasFocusedItem) {
        console.log("🗑️ Has single focused item (renaming), skipping");
        return;
    }

    // Handle single item deletion (existing logic)
    if (selectedItems.length === 1) {
        console.log("🗑️ Single item deletion");
        await deleteSingleItem(plugin, selectedItems[0], e, tree);
        return;
    }

    // Handle multiple items deletion
    console.log("🗑️ Multiple items deletion");
    await deleteMultipleItems(plugin, selectedItems, e, tree);
}

function getSelectedItemsForDelete(plugin: ExplorerShortcuts): any[] {
    const fileExplorer = plugin.app.workspace.getLeavesOfType("file-explorer")[0];
    if (!fileExplorer) return [];

    const view = fileExplorer.view as any;
    if (!view) return [];

    const selectedItems: any[] = [];
    const fileItems = view.fileItems;
    const candidateItems: { path: string; item: any; isFolder: boolean }[] = [];

    // First pass: collect all items that have is-selected class
    for (const path in fileItems) {
        const item = fileItems[path];
        if (!item?.el) continue;

        let isSelected = false;

        if (item.el.classList.contains("is-selected")) {
            isSelected = true;
        } else {
            const titleEl = item.el.querySelector(".nav-file-title, .nav-folder-title");
            if (titleEl && titleEl.classList.contains("is-selected")) {
                isSelected = true;
            } else {
                const selectedChild = item.el.querySelector(".is-selected");
                if (selectedChild) {
                    isSelected = true;
                }
            }
        }

        if (isSelected) {
            candidateItems.push({
                path,
                item,
                isFolder: item.el.classList.contains("nav-folder")
            });
        }
    }

    // If only one item is selected, return it regardless of type
    if (candidateItems.length === 1) {
        return [candidateItems[0].item];
    }

    // For multiple selections, filter out parent folders (same logic as cut-copy)
    const selectedFilePaths = candidateItems
        .filter(candidate => !candidate.isFolder)
        .map(candidate => candidate.path);

    // If we have selected files, exclude any folders that contain them
    if (selectedFilePaths.length > 0) {
        for (const candidate of candidateItems) {
            if (candidate.isFolder) {
                // For folders, only include if no selected files are inside this folder
                const hasSelectedFilesInside = selectedFilePaths.some(filePath =>
                    filePath.startsWith(candidate.path + "/")
                );

                if (!hasSelectedFilesInside) {
                    selectedItems.push(candidate.item);
                }
            } else {
                // Always include files
                selectedItems.push(candidate.item);
            }
        }
    } else {
        // If no files are selected, include all folders
        selectedItems.push(...candidateItems.map(candidate => candidate.item));
    }

    return selectedItems;
}

async function deleteSingleItem(plugin: ExplorerShortcuts, item: any, e: KeyboardEvent, tree: any): Promise<void> {
    let confirmed = true;
    const itemFile = item.file;

    if (plugin.settings.delConfirmFile && itemFile instanceof TFile) {
        confirmed = await getConfirmed(itemFile);
    } else if (plugin.settings.delConfirmFolder && itemFile instanceof TFolder) {
        const isFolderEmpty = itemFile.children.length === 0;
        if (!isFolderEmpty) {
            confirmed = await getConfirmed(itemFile);
        }
    }

    if (!confirmed) return;

    tree.selectItem(item);
    tree.handleDeleteSelectedItems(e);
    const text = itemFile instanceof TFile ? "File" : "Folder";
    new Notice(`${text} removed: ` + itemFile.name, 3500);
}

async function deleteMultipleItems(plugin: ExplorerShortcuts, items: any[], e: KeyboardEvent, tree: any): Promise<void> {
    let applyToAll = false;
    let skipAll = false;
    let cancelled = false;

    for (let i = 0; i < items.length && !cancelled; i++) {
        const item = items[i];
        const itemFile = item.file;
        let shouldDelete = true;

        // Check if confirmation is needed
        const needsConfirmation =
            (plugin.settings.delConfirmFile && itemFile instanceof TFile) ||
            (plugin.settings.delConfirmFolder && itemFile instanceof TFolder && itemFile.children.length > 0);

        if (needsConfirmation && !applyToAll && !skipAll) {
            const action = await getMultiDeleteConfirmation(plugin.app, itemFile, items.length - i);

            if (action.action === 'cancel') {
                cancelled = true;
                break;
            }

            shouldDelete = action.action === 'delete';

            if (action.applyToAll) {
                if (action.action === 'delete') {
                    applyToAll = true;
                } else {
                    skipAll = true;
                }
            }
        } else if (skipAll) {
            shouldDelete = false;
        }

        if (shouldDelete) {
            try {
                if (itemFile instanceof TFile) {
                    await plugin.app.vault.delete(itemFile);
                } else if (itemFile instanceof TFolder) {
                    await plugin.app.vault.delete(itemFile);
                }
                const text = itemFile instanceof TFile ? "File" : "Folder";
                new Notice(`${text} removed: ` + itemFile.name, 2000);
            } catch (error) {
                console.error(`Failed to delete ${itemFile.name}:`, error);
                new Notice(`Failed to delete ${itemFile.name}. Check console for details.`);
            }
        }
    }
}

async function getConfirmed(itemFile: TFile | TFolder): Promise<boolean> {
    return confirmation(" Are you sure you want to delete " + itemFile.name + "?");
}

async function getMultiDeleteConfirmation(app: any, itemFile: TFile | TFolder, remainingCount: number): Promise<DeleteAction> {
    return new Promise((resolve) => {
        const modal = new Modal(app);
        modal.titleEl.textContent = `Delete ${itemFile instanceof TFile ? 'file' : 'folder'}`;

        const content = modal.contentEl;
        content.empty();

        content.createEl("p", { text: `Are you sure you want to delete "${itemFile.name}"?` });
        if (remainingCount > 1) {
            content.createEl("p", { text: `${remainingCount} items remaining.` });
        }

        let applyToAll = false;

        if (remainingCount > 1) {
            new Setting(content)
                .setName("Apply to all remaining items")
                .addToggle((toggle: any) => {
                    toggle.onChange((value: boolean) => {
                        applyToAll = value;
                    });
                });
        }

        new Setting(content)
            .addButton((btn: any) => {
                btn.setButtonText("Delete")
                    .setCta()
                    .onClick(() => {
                        resolve({ action: 'delete', applyToAll });
                        modal.close();
                    });
            })
            .addButton((btn: any) => {
                btn.setButtonText("Skip")
                    .onClick(() => {
                        resolve({ action: 'skip', applyToAll });
                        modal.close();
                    });
            })
            .addButton((btn: any) => {
                btn.setButtonText("Cancel")
                    .onClick(() => {
                        resolve({ action: 'cancel' });
                        modal.close();
                    });
            });

        modal.open();
    });
}

export function triggerDelete(plugin: ExplorerShortcuts, evt: KeyboardEvent): void {
    // trigger a mouse move event to refresh the selectedElements
    const e = new MouseEvent('mousemove', { clientX: plugin.mousePosition.x + 1, clientY: plugin.mousePosition.y + 1 });
    setTimeout(() => {
        document.dispatchEvent(e);
    }, 70);
}
